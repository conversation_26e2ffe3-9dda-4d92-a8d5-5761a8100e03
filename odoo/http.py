# Part of Odoo. See LICENSE file for full copyright and licensing details.
r"""\
Odoo HTTP layer / WSGI application

This module has been refactored into smaller, focused modules for better
maintainability and separation of concerns. The original functionality
is preserved through imports from the sub-modules.

The main components are now organized as follows:

- odoo.http.utils: Utility functions and constants
- odoo.http.stream: File streaming utilities  
- odoo.http.controllers: Controller base class and route decorator
- odoo.http.session: Session management classes
- odoo.http.request_response: Request and Response classes
- odoo.http.dispatchers: Request dispatching logic
- odoo.http.application: WSGI application class

All original functionality remains available through this module for
backward compatibility.
"""

# Import everything from the refactored modules to maintain backward compatibility
from .http import *
