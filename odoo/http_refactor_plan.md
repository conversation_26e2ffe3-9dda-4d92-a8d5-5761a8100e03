# HTTP Module Refactoring Plan

## Current State
The `odoo/http.py` file is monolithic with 2581 lines containing multiple responsibilities.

## Proposed Structure

### 1. Core HTTP Module (`odoo/http.py`)
- Keep main exports and imports for backward compatibility
- Import and re-export from sub-modules
- Keep essential utility functions
- ~200-300 lines

### 2. Stream Module (`odoo/http/stream.py`)
- `Stream` class and related utilities
- File streaming, binary field handling
- ~200 lines

### 3. Controllers Module (`odoo/http/controllers.py`)
- `Controller` base class
- Route decorator and routing utilities
- Controller inheritance mechanism
- ~300 lines

### 4. Session Module (`odoo/http/session.py`)
- `Session` class
- `FilesystemSessionStore` class
- Session management utilities
- ~400 lines

### 5. Request/Response Module (`odoo/http/request_response.py`)
- `Request` class
- `Response` classes (`_Response`, `FutureResponse`)
- `HTTPRequest` class
- Request/response utilities
- ~800 lines

### 6. Dispatchers Module (`odoo/http/dispatchers.py`)
- `Dispatcher` base class
- `HttpDispatcher` class
- `JsonRPCDispatcher` class
- ~400 lines

### 7. Application Module (`odoo/http/application.py`)
- `Application` WSGI class
- Static file handling
- Main application logic
- ~300 lines

### 8. Utils Module (`odoo/http/utils.py`)
- `GeoIP` class
- Utility functions (db_list, db_filter, etc.)
- Helper functions
- ~300 lines

## Benefits
1. **Improved maintainability** - Smaller, focused modules
2. **Better testability** - Isolated components
3. **Clearer separation of concerns** - Each module has a single responsibility
4. **Easier navigation** - Developers can find relevant code faster
5. **Reduced cognitive load** - Smaller files are easier to understand

## Implementation Strategy
1. Create new module structure
2. Move classes/functions to appropriate modules
3. Update imports in main http.py
4. Ensure backward compatibility
5. Update tests if needed

## Backward Compatibility
All existing imports from `odoo.http` will continue to work through re-exports in the main module.
