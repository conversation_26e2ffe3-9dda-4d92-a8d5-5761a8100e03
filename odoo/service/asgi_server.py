# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
ASGI server implementation for Odoo.
Replaces the WSGI-based servers with async ASGI servers using Uvicorn.
"""

import asyncio
import logging
import os
import signal
import threading
import time
from contextlib import asynccontextmanager

import uvicorn
from uvicorn.config import Config
from uvicorn.server import Server

import odoo
from odoo.tools import config
from .server import CommonServer, set_limit_memory_hard, dumpstacks, log_ormcache_stats

_logger = logging.getLogger(__name__)


class AsyncServer(CommonServer):
    """Async ASGI server using Uvicorn."""
    
    def __init__(self, app):
        super().__init__(app)
        self.server = None
        self.server_task = None
        self.loop = None

    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        if os.name == 'posix':
            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)
            signal.signal(signal.SIGCHLD, self.signal_handler)
            signal.signal(signal.SIGHUP, self.signal_handler)
            signal.signal(signal.SIGXCPU, self.signal_handler)
            signal.signal(signal.SIGQUIT, dumpstacks)
            signal.signal(signal.SIGUSR1, log_ormcache_stats)

    def signal_handler(self, sig, frame):
        """Handle shutdown signals."""
        _logger.info(f"Received signal {sig}, shutting down ASGI server")
        if self.server:
            self.server.should_exit = True

    async def start_async(self):
        """Start the ASGI server asynchronously."""
        set_limit_memory_hard()
        self.setup_signal_handlers()

        # Configure Uvicorn
        config_dict = {
            'app': self.app,
            'host': self.interface,
            'port': self.port,
            'log_level': 'info',
            'access_log': True,
            'loop': 'asyncio',
            'lifespan': 'on',
        }

        # Add SSL configuration if available
        if config.get('ssl_cert') and config.get('ssl_key'):
            config_dict.update({
                'ssl_certfile': config['ssl_cert'],
                'ssl_keyfile': config['ssl_key'],
            })

        uvicorn_config = Config(**config_dict)
        self.server = Server(uvicorn_config)
        
        _logger.info('ASGI service (uvicorn) starting on %s:%s', self.interface, self.port)
        await self.server.serve()

    def start(self, stop=False):
        """Start the server."""
        _logger.debug("Starting ASGI server")
        
        # Create new event loop for the server
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        try:
            self.loop.run_until_complete(self.start_async())
        except KeyboardInterrupt:
            _logger.info("ASGI server interrupted")
        finally:
            self.loop.close()

    def stop(self):
        """Stop the ASGI server."""
        _logger.info("Stopping ASGI server")
        if self.server:
            self.server.should_exit = True
        if self.loop and not self.loop.is_closed():
            self.loop.stop()

    def run(self, preload=None, stop=False):
        """Run the server with optional preloading."""
        if stop:
            return 0
        
        try:
            self.start(stop=stop)
            return 0
        except Exception as e:
            _logger.error("Error running ASGI server: %s", e, exc_info=True)
            return 1


class AsyncGeventServer(AsyncServer):
    """ASGI server with gevent compatibility."""
    
    def __init__(self, app):
        super().__init__(app)
        self.port = config['gevent_port']

    async def start_async(self):
        """Start with gevent-specific configurations."""
        import gevent
        
        # Setup gevent-specific signal handlers
        if os.name == 'posix':
            signal.signal(signal.SIGQUIT, dumpstacks)
            signal.signal(signal.SIGUSR1, log_ormcache_stats)
            
        await super().start_async()

    def watchdog(self, beat=4):
        """Process monitoring watchdog."""
        import gevent
        self.ppid = os.getppid()
        while True:
            self.process_limits()
            gevent.sleep(beat)

    def process_limits(self):
        """Check process limits and restart if necessary."""
        restart = False
        if hasattr(self, 'ppid') and self.ppid != os.getppid():
            _logger.warning("ASGI Parent changed: %s", self.pid)
            restart = True
        
        # Check memory limits
        try:
            import psutil
            memory = psutil.Process(self.pid).memory_info().vms
            limit_memory_soft = config['limit_memory_soft_gevent'] or config['limit_memory_soft']
            if limit_memory_soft and memory > limit_memory_soft:
                _logger.warning('ASGI virtual memory limit reached: %s', memory)
                restart = True
        except ImportError:
            pass
        
        if restart:
            os.kill(self.pid, signal.SIGTERM)


class AsyncPreforkServer(AsyncServer):
    """Multi-process ASGI server implementation."""
    
    def __init__(self, app):
        super().__init__(app)
        self.workers = config['workers']

    async def start_async(self):
        """Start with multiple worker processes."""
        config_dict = {
            'app': self.app,
            'host': self.interface,
            'port': self.port,
            'workers': self.workers,
            'log_level': 'info',
            'access_log': True,
            'loop': 'asyncio',
        }

        uvicorn_config = Config(**config_dict)
        self.server = Server(uvicorn_config)
        
        _logger.info('ASGI service (uvicorn) starting with %d workers on %s:%s', 
                    self.workers, self.interface, self.port)
        await self.server.serve()


def create_asgi_server(app):
    """Factory function to create the appropriate ASGI server."""
    if odoo.evented:
        return AsyncGeventServer(app)
    elif config['workers']:
        return AsyncPreforkServer(app)
    else:
        return AsyncServer(app)


# Async server startup functions
async def async_start(preload=None, stop=False):
    """Async version of server start function."""
    from odoo.service.server import load_server_wide_modules
    
    load_server_wide_modules()
    
    # Import the ASGI application
    from odoo.http.asgi_application import async_root
    
    server = create_asgi_server(async_root)
    return server.run(preload, stop)


def start_asgi_server(preload=None, stop=False):
    """Start the ASGI server in a new event loop."""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        return loop.run_until_complete(async_start(preload, stop))
    except KeyboardInterrupt:
        _logger.info("ASGI server startup interrupted")
        return 0
    except Exception as e:
        _logger.error("Error starting ASGI server: %s", e, exc_info=True)
        return 1
    finally:
        if loop and not loop.is_closed():
            loop.close()
