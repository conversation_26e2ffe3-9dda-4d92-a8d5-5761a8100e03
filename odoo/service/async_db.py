# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async database service operations for Odoo.
This module provides async versions of database management functions.
"""

import asyncio
import logging
import os
import shutil
import tempfile
from contextlib import asynccontextmanager
from datetime import datetime
from xml.etree import ElementTree as ET

import asyncpg
from decorator import decorator
from pytz import country_timezones

import odoo
import odoo.release
import odoo.sql_db
import odoo.tools
from odoo.async_sql_db import async_db_connect, connection_info_for
from odoo.tools import config, db_list, existing_tables
from odoo.tools.misc import file_path

_logger = logging.getLogger(__name__)


def check_db_management_enabled(method):
    """Decorator to check if database management is enabled."""
    def wrapper(*args, **kwargs):
        if not config['list_db']:
            _logger.error('Database management functions blocked, admin disabled database listing')
            raise odoo.exceptions.AccessDenied()
        return method(*args, **kwargs)
    return wrapper


async def async_check_faketime_mode(db_name):
    """Check and configure faketime mode for database asynchronously."""
    if not config.get('test_enable'):
        return
    
    try:
        connection = await async_db_connect(db_name)
        async with connection.cursor() as cursor:
            # Set timezone and fake time for testing
            time_offset = config.get('test_time_offset', 0)
            await cursor.execute("""
                SET timezone = 'UTC';
                SELECT set_config('timezone', 'UTC', false);
                SELECT set_config('test.time_offset', %s, false);
            """, (int(time_offset),))
            
            await cursor.execute("SELECT (now() AT TIME ZONE 'UTC');")
            result = await cursor.fetchone()
            new_now = result[0] if result else None
            _logger.info("Faketime mode, new cursor now is %s", new_now)
            await cursor.commit()
    except Exception as e:
        _logger.warning("Unable to set fakedtimed NOW() : %s", e)


async def async_create_empty_database(name):
    """Create an empty database asynchronously."""
    connection = await async_db_connect('postgres')
    async with connection.cursor() as cr:
        # Database-altering operations cannot be executed inside a transaction
        await cr.execute("SET autocommit = true")
        
        # Check if database already exists
        await cr.execute("SELECT 1 FROM pg_database WHERE datname = %s", (name,))
        if await cr.fetchone():
            raise Exception(f"Database {name} already exists")
        
        # Create the database
        await cr.execute(f'CREATE DATABASE "{name}" ENCODING \'unicode\'')
        _logger.info('Created database %s', name)


async def async_create_database_extensions(name):
    """Create PostgreSQL extensions for the database asynchronously."""
    try:
        connection = await async_db_connect(name)
        async with connection.cursor() as cr:
            await cr.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm")
            if odoo.tools.config['unaccent']:
                await cr.execute("CREATE EXTENSION IF NOT EXISTS unaccent")
                await cr.execute("ALTER FUNCTION unaccent(text) IMMUTABLE")
    except Exception as e:  # Changed from psycopg2.Error
        _logger.warning("Unable to create PostgreSQL extensions : %s", e)
    
    await async_check_faketime_mode(name)

    # Restore legacy behaviour on pg15+
    try:
        connection = await async_db_connect(name)
        async with connection.cursor() as cr:
            await cr.execute("GRANT CREATE ON SCHEMA PUBLIC TO PUBLIC")
    except Exception as e:  # Changed from psycopg2.Error
        _logger.warning("Unable to make public schema public-accessible: %s", e)


@check_db_management_enabled
async def async_exp_create_database(db_name, demo, lang, user_password='admin', login='admin', country_code=None, phone=None):
    """Create a new database asynchronously."""
    _logger.info('Create database `%s`.', db_name)
    await async_create_empty_database(db_name)
    await async_initialize_db(db_name, demo, lang, user_password, login, country_code, phone)
    return True


async def async_initialize_db(db_name, demo, lang, user_password='admin', login='admin', country_code=None, phone=None):
    """Initialize a database with base modules asynchronously."""
    try:
        # TODO: Implement async database initialization
        # This would involve:
        # 1. Installing base modules
        # 2. Creating admin user
        # 3. Setting up initial data
        # 4. Running post-install hooks
        
        connection = await async_db_connect(db_name)
        async with connection.cursor() as cr:
            # Basic initialization
            await cr.execute("""
                CREATE TABLE IF NOT EXISTS ir_module_module (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(64) NOT NULL UNIQUE,
                    state VARCHAR(16) DEFAULT 'uninstalled'
                )
            """)
            await cr.commit()
        
        _logger.info('Database %s initialized successfully', db_name)
        
    except Exception as e:
        _logger.error('Failed to initialize database %s: %s', db_name, e, exc_info=True)
        raise


@check_db_management_enabled
async def async_exp_drop(db_name):
    """Drop a database asynchronously."""
    if db_name not in db_list(True):
        return False
    
    # TODO: Clean up registry and close connections
    # odoo.modules.registry.Registry.delete(db_name)
    # await async_close_db(db_name)

    connection = await async_db_connect('postgres')
    async with connection.cursor() as cr:
        # Database-altering operations cannot be executed inside a transaction
        await cr.execute("SET autocommit = true")
        
        # Drop connections to the database
        await cr.execute("""
            SELECT pg_terminate_backend(pid)
            FROM pg_stat_activity
            WHERE datname = %s AND pid <> pg_backend_pid()
        """, (db_name,))

        try:
            await cr.execute(f'DROP DATABASE "{db_name}"')
        except Exception as e:
            _logger.info('DROP DB: %s failed:\n%s', db_name, e)
            raise Exception("Couldn't drop database %s: %s" % (db_name, e))
        else:
            _logger.info('DROP DB: %s', db_name)

    # Remove filestore
    fs = odoo.tools.config.filestore(db_name)
    if os.path.exists(fs):
        shutil.rmtree(fs)
    return True


async def async_exp_db_exist(db_name):
    """Check if database exists asynchronously."""
    try:
        connection = await async_db_connect(db_name)
        async with connection.cursor():
            return True
    except Exception:
        return False


async def async_exp_list(document=False):
    """List databases asynchronously."""
    if not config['list_db']:
        raise odoo.exceptions.AccessDenied()
    
    try:
        connection = await async_db_connect('postgres')
        async with connection.cursor() as cr:
            await cr.execute("""
                SELECT datname 
                FROM pg_database 
                WHERE datdba = (SELECT usesysid FROM pg_user WHERE usename = current_user)
                AND NOT datistemplate
                ORDER BY datname
            """)
            result = await cr.fetchall()
            databases = [row[0] for row in result]
            
        if document:
            # Return XML document format
            doc = ET.Element('databases')
            for db in databases:
                db_elem = ET.SubElement(doc, 'database')
                db_elem.text = db
            return ET.tostring(doc, encoding='unicode')
        
        return databases
        
    except Exception as e:
        _logger.error('Error listing databases: %s', e, exc_info=True)
        return []


async def async_exp_change_admin_password(new_password):
    """Change admin password asynchronously."""
    config['admin_passwd'] = new_password
    config.save()
    return True


async def async_exp_migrate_databases(databases):
    """Migrate databases asynchronously."""
    for db_name in databases:
        _logger.info('Migrating database %s', db_name)
        try:
            # TODO: Implement async database migration
            connection = await async_db_connect(db_name)
            async with connection.cursor() as cr:
                # Run migration scripts
                await cr.execute("SELECT 1")  # Placeholder
                await cr.commit()
            _logger.info('Database %s migrated successfully', db_name)
        except Exception as e:
            _logger.error('Failed to migrate database %s: %s', db_name, e, exc_info=True)


# Async database management API
class AsyncDatabaseManager:
    """Async database management interface."""
    
    @staticmethod
    async def create_database(name, demo=False, lang='en_US', **kwargs):
        """Create a database asynchronously."""
        return await async_exp_create_database(name, demo, lang, **kwargs)
    
    @staticmethod
    async def drop_database(name):
        """Drop a database asynchronously."""
        return await async_exp_drop(name)
    
    @staticmethod
    async def list_databases():
        """List databases asynchronously."""
        return await async_exp_list()
    
    @staticmethod
    async def database_exists(name):
        """Check if database exists asynchronously."""
        return await async_exp_db_exist(name)
    
    @staticmethod
    async def migrate_databases(databases):
        """Migrate databases asynchronously."""
        return await async_migrate_databases(databases)


# Global async database manager instance
async_db_manager = AsyncDatabaseManager()
