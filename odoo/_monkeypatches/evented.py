"""
Running mode flags (async, gevent, prefork)

This should be imported as early as possible.
It will initialize the `odoo.evented` and `odoo.async_mode` variables.
"""
import asyncio
import odoo
import sys

odoo.evented = False
odoo.async_mode = False


def patch_async():
    """Enable async mode for ASGI operation."""
    if odoo.async_mode or not (len(sys.argv) > 1 and sys.argv[1] == 'async'):
        return

    sys.argv.remove('async')

    # Set async mode flag
    odoo.async_mode = True

    # Configure asyncio for optimal performance
    if hasattr(asyncio, 'set_event_loop_policy'):
        # Use uvloop if available for better performance
        try:
            import uvloop
            asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())
        except ImportError:
            # Fall back to default asyncio policy
            pass


def patch_evented():
    """Legacy gevent support - now redirects to async mode."""
    if odoo.evented or not (len(sys.argv) > 1 and sys.argv[1] == 'gevent'):
        return

    # Redirect gevent mode to async mode
    _logger = __import__('logging').getLogger(__name__)
    _logger.warning("Gevent mode is deprecated. Switching to async mode.")

    sys.argv.remove('gevent')
    sys.argv.append('async')

    # Enable async mode instead of gevent
    patch_async()

    # Keep evented flag for backward compatibility
    odoo.evented = True


def patch_all():
    """Apply all patches based on command line arguments."""
    patch_async()
    patch_evented()
