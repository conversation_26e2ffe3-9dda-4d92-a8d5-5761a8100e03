# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async controllers for Odoo ASGI application.
This module provides async versions of HTTP controllers and route decorators.
"""

import asyncio
import collections
import functools
import inspect
import logging

from odoo.tools import unique
from odoo.tools.func import filter_kwargs

_logger = logging.getLogger(__name__)


class AsyncController:
    """
    Async version of the Controller class.
    
    Base class for async HTTP controllers that can serve content over HTTP
    using async/await patterns. Each class inheriting from AsyncController
    can use the async_route decorator to route matching incoming web requests
    to decorated async methods.
    
    Example:
        class AsyncGreetingController(AsyncController):
            @async_route('/async_greet', type='http', auth='public')
            async def greeting(self):
                # Async database operations
                result = await some_async_db_operation()
                return f'Hello {result}'
    """
    pass


def async_route(route=None, **kw):
    """
    Async version of the route decorator.
    
    Decorator to expose async controller methods as HTTP endpoints.
    This decorator marks methods as async route handlers and configures
    their routing parameters.
    
    :param str route: URL pattern for the route
    :param str type: Request type ('http' or 'json')
    :param str auth: Authentication requirement ('public', 'user', 'admin')
    :param list methods: HTTP methods allowed (['GET', 'POST', etc.])
    :param bool cors: Enable CORS support
    :param bool csrf: Enable CSRF protection
    :param bool save_session: Save session after request
    :param bool readonly: Mark as read-only operation
    """
    
    def decorator(func):
        if not asyncio.iscoroutinefunction(func):
            raise ValueError(f"Function {func.__name__} must be async to use async_route decorator")
        
        # Store routing information
        routing = kw.copy()
        routing['route'] = route
        
        # Mark as async route
        func.routing = routing
        func._async_route = True
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Pre-processing for async routes
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                _logger.error(f"Error in async route {func.__name__}: {e}", exc_info=True)
                raise
        
        wrapper.routing = routing
        wrapper._async_route = True
        wrapper.original_func = func
        
        return wrapper
    
    return decorator


def async_json_route(route=None, **kw):
    """Async decorator for JSON routes."""
    kw['type'] = 'json'
    return async_route(route, **kw)


def async_http_route(route=None, **kw):
    """Async decorator for HTTP routes."""
    kw['type'] = 'http'
    return async_route(route, **kw)


class AsyncRouteRegistry:
    """Registry for async routes."""
    
    def __init__(self):
        self._routes = {}
        self._controllers = {}

    def register_controller(self, controller_class):
        """Register an async controller class."""
        controller_name = controller_class.__name__
        self._controllers[controller_name] = controller_class
        
        # Register all async routes from the controller
        for method_name in dir(controller_class):
            method = getattr(controller_class, method_name)
            if hasattr(method, '_async_route') and hasattr(method, 'routing'):
                route_pattern = method.routing.get('route')
                if route_pattern:
                    self._routes[route_pattern] = {
                        'controller': controller_class,
                        'method': method,
                        'routing': method.routing
                    }

    def get_route(self, path):
        """Get route handler for a given path."""
        return self._routes.get(path)

    def get_all_routes(self):
        """Get all registered routes."""
        return self._routes.copy()


# Global async route registry
async_route_registry = AsyncRouteRegistry()


def register_async_controller(controller_class):
    """Register an async controller class globally."""
    async_route_registry.register_controller(controller_class)
    return controller_class


class AsyncDispatcher:
    """Async request dispatcher."""
    
    def __init__(self, request):
        self.request = request

    async def dispatch(self, endpoint, args):
        """Dispatch request to async endpoint."""
        try:
            if hasattr(endpoint, '_async_route'):
                # This is an async route
                if inspect.ismethod(endpoint):
                    result = await endpoint(*args)
                else:
                    # Create controller instance and call method
                    controller = endpoint.__self__.__class__()
                    method = getattr(controller, endpoint.__name__)
                    result = await method(*args)
                return result
            else:
                # Fallback to sync route (wrapped in async)
                if inspect.ismethod(endpoint):
                    result = endpoint(*args)
                else:
                    controller = endpoint.__self__.__class__()
                    method = getattr(controller, endpoint.__name__)
                    result = method(*args)
                return result
        except Exception as e:
            _logger.error(f"Error dispatching to {endpoint}: {e}", exc_info=True)
            raise

    async def handle_error(self, exc):
        """Handle errors in async request processing."""
        # TODO: Implement async error handling
        from .async_request_response import AsyncResponse
        return AsyncResponse(f"Error: {exc}", status=500)


def async_generate_routing_rules(modules, nodb_only=False):
    """Generate routing rules for async controllers."""
    # TODO: Implement async routing rule generation
    # This should scan modules for async controllers and generate routes
    rules = []
    
    for module_name in modules:
        try:
            # Import module and scan for async controllers
            module = __import__(f'odoo.addons.{module_name}.controllers', fromlist=[''])
            
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (inspect.isclass(attr) and 
                    issubclass(attr, AsyncController) and 
                    attr is not AsyncController):
                    
                    # Register the controller
                    register_async_controller(attr)
                    
                    # Generate rules for its methods
                    for method_name in dir(attr):
                        method = getattr(attr, method_name)
                        if hasattr(method, '_async_route') and hasattr(method, 'routing'):
                            routing = method.routing
                            route_pattern = routing.get('route')
                            if route_pattern:
                                rules.append((route_pattern, method))
        
        except ImportError:
            # Module doesn't exist or doesn't have controllers
            continue
    
    return rules


# Compatibility functions for existing code
def _async_generate_routing_rules(modules, nodb_only=False):
    """Wrapper for async routing rule generation."""
    return async_generate_routing_rules(modules, nodb_only)


# Async middleware for route processing
class AsyncRouteMiddleware:
    """Middleware to handle async route processing."""
    
    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        """ASGI middleware interface."""
        if scope['type'] == 'http':
            path = scope['path']
            route_info = async_route_registry.get_route(path)
            
            if route_info:
                # Handle async route
                try:
                    controller_class = route_info['controller']
                    method = route_info['method']
                    
                    # Create controller instance
                    controller = controller_class()
                    
                    # Call async method
                    result = await method(controller)
                    
                    # Convert result to ASGI response
                    if hasattr(result, '__call__'):
                        await result(scope, receive, send)
                    else:
                        # Simple string/bytes response
                        await send({
                            'type': 'http.response.start',
                            'status': 200,
                            'headers': [[b'content-type', b'text/html']],
                        })
                        await send({
                            'type': 'http.response.body',
                            'body': str(result).encode('utf-8'),
                        })
                except Exception as e:
                    _logger.error(f"Error in async route: {e}", exc_info=True)
                    await send({
                        'type': 'http.response.start',
                        'status': 500,
                        'headers': [],
                    })
                    await send({
                        'type': 'http.response.body',
                        'body': b'Internal Server Error',
                    })
            else:
                # Pass to next middleware/app
                await self.app(scope, receive, send)
        else:
            # Pass non-HTTP requests to next middleware/app
            await self.app(scope, receive, send)
