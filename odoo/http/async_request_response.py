# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async request/response handling for Odoo ASGI application.
This module provides async versions of the HTTP request and response classes.
"""

import asyncio
import contextlib
import functools
import logging
from datetime import datetime, timedelta

import werkzeug.exceptions
import werkzeug.wrappers
import werkzeug.datastructures
import werkzeug.local
from starlette.requests import Request as StarletteRequest
from starlette.responses import Response as StarletteResponse

from odoo.tools.facade import Proxy, ProxyFunc, ProxyAttr
from odoo.tools._vendor.useragents import UserAgent

from .utils import DEFAULT_MAX_CONTENT_LENGTH

_logger = logging.getLogger(__name__)

# Async-safe request stack using asyncio context variables
import contextvars
_async_request_context = contextvars.ContextVar('async_request', default=None)


@contextlib.asynccontextmanager
async def async_borrow_request():
    """Get the current async request from context."""
    req = _async_request_context.get()
    try:
        yield req
    finally:
        pass  # Context variables handle cleanup automatically


class AsyncHTTPRequest:
    """Async wrapper for HTTP requests."""
    
    def __init__(self, starlette_request: StarletteRequest):
        self._starlette_request = starlette_request
        self._body = None
        self._form = None
        self._files = None

    @property
    def method(self):
        return self._starlette_request.method

    @property
    def url(self):
        return str(self._starlette_request.url)

    @property
    def path(self):
        return self._starlette_request.url.path

    @property
    def query_string(self):
        return self._starlette_request.url.query

    @property
    def headers(self):
        return dict(self._starlette_request.headers)

    @property
    def remote_addr(self):
        return self._starlette_request.client.host if self._starlette_request.client else ''

    async def get_body(self):
        """Get request body asynchronously."""
        if self._body is None:
            self._body = await self._starlette_request.body()
        return self._body

    async def get_form(self):
        """Get form data asynchronously."""
        if self._form is None:
            form_data = await self._starlette_request.form()
            self._form = dict(form_data)
        return self._form

    async def get_json(self):
        """Get JSON data asynchronously."""
        return await self._starlette_request.json()

    def get_cookie(self, name, default=None):
        """Get cookie value."""
        return self._starlette_request.cookies.get(name, default)

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


class AsyncRequest:
    """Async version of Odoo's Request class."""
    
    def __init__(self, httprequest: AsyncHTTPRequest):
        self.httprequest = httprequest
        self.db = None
        self.session = None
        self.dispatcher = None
        self._context = {}

    async def _async_post_init(self):
        """Async initialization after request creation."""
        # Initialize session, database connection, etc.
        await self._init_session()
        await self._init_database()
        await self._init_dispatcher()

    async def _init_session(self):
        """Initialize session asynchronously."""
        # TODO: Implement async session initialization
        pass

    async def _init_database(self):
        """Initialize database connection asynchronously."""
        # TODO: Implement async database initialization
        pass

    async def _init_dispatcher(self):
        """Initialize request dispatcher asynchronously."""
        # TODO: Implement async dispatcher initialization
        pass

    async def _async_serve_static(self):
        """Serve static files asynchronously."""
        # TODO: Implement async static file serving
        return AsyncResponse("Static file", status=200)

    async def _async_serve_db(self):
        """Serve database-backed requests asynchronously."""
        # TODO: Implement async database request handling
        return AsyncResponse("Database response", status=200)

    async def _async_serve_nodb(self):
        """Serve requests without database asynchronously."""
        # TODO: Implement async no-database request handling
        return AsyncResponse("No database response", status=200)

    def reroute(self, path, query_string=None):
        """Reroute the request to a different path."""
        # TODO: Implement rerouting for async requests
        pass


class AsyncResponse:
    """Async response wrapper."""
    
    def __init__(self, content, status=200, headers=None, content_type='text/html'):
        self.content = content
        self.status = status
        self.headers = headers or {}
        self.content_type = content_type

    def to_starlette_response(self):
        """Convert to Starlette response."""
        if isinstance(self.content, str):
            content = self.content.encode('utf-8')
        else:
            content = self.content

        headers = dict(self.headers)
        headers['content-type'] = self.content_type

        return StarletteResponse(
            content=content,
            status_code=self.status,
            headers=headers
        )

    async def __call__(self, scope, receive, send):
        """ASGI response interface."""
        response = self.to_starlette_response()
        await response(scope, receive, send)


class Headers:
    """Headers wrapper for async responses."""
    
    def __init__(self, headers=None):
        self._headers = headers or {}

    def __getitem__(self, key):
        return self._headers[key]

    def __setitem__(self, key, value):
        self._headers[key] = value

    def get(self, key, default=None):
        return self._headers.get(key, default)

    def items(self):
        return self._headers.items()


class ResponseCacheControl:
    """Cache control for async responses."""
    
    def __init__(self):
        self.max_age = None
        self.no_cache = False
        self.no_store = False

    def to_header(self):
        """Convert to cache-control header value."""
        parts = []
        if self.max_age is not None:
            parts.append(f'max-age={self.max_age}')
        if self.no_cache:
            parts.append('no-cache')
        if self.no_store:
            parts.append('no-store')
        return ', '.join(parts)


class ResponseStream:
    """Streaming response for async operations."""
    
    def __init__(self, generator):
        self.generator = generator

    async def stream(self):
        """Stream response data asynchronously."""
        async for chunk in self.generator:
            yield chunk


# Async request context management
def get_async_request():
    """Get the current async request from context."""
    return _async_request_context.get()


def set_async_request(request):
    """Set the current async request in context."""
    _async_request_context.set(request)


async def abort(status_code, description=None):
    """Async version of abort function."""
    raise werkzeug.exceptions.HTTPException(
        response=AsyncResponse(
            description or f"HTTP {status_code}",
            status=status_code
        )
    )


# Compatibility layer for existing code
class AsyncRequestProxy:
    """Proxy to provide sync-like interface for async requests."""
    
    def __init__(self):
        self._current_request = None

    def __getattr__(self, name):
        request = get_async_request()
        if request is None:
            raise RuntimeError("No async request in current context")
        return getattr(request, name)


# Global async request proxy for backward compatibility
async_request = AsyncRequestProxy()
