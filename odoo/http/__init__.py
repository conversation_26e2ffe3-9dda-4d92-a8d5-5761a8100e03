# Part of Odoo. See LICENSE file for full copyright and licensing details.

# Import all components to maintain backward compatibility
from .utils import *
from .stream import *
from .controllers import *
from .session import *
from .request_response import *
from .request import *
from .dispatchers import *
from .application import *

# Re-export everything that was previously available in odoo.http
__all__ = [
    # Utils
    'get_default_session', 'content_disposition', 'db_list', 'db_filter',
    'dispatch_rpc', 'get_session_max_inactivity', 'is_cors_preflight',
    'serialize_exception', 'GeoIP', 'RegistryError', 'SessionExpiredException',
    'DEFAULT_MAX_CONTENT_LENGTH', 'SESSION_LIFETIME', 'STATIC_CACHE',
    'STATIC_CACHE_LONG', 'JSON_MIMETYPES', 'MISSING_CSRF_WARNING', 'ROUTING_KEYS',

    # Stream
    'Stream',

    # Controllers
    'Controller', 'route',

    # Session
    'Session', 'FilesystemSessionStore',

    # Request/Response
    'Request', 'Response', 'HTTPRequest', 'FutureResponse', 'Headers',
    'ResponseCacheControl', 'ResponseStream', 'request', 'borrow_request', 'abort',

    # Dispatchers
    'Dispatcher', 'HttpDispatcher', 'JsonRPCDispatcher',

    # Application
    'Application', 'root',
]
