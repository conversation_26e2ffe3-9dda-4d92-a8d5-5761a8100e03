#!/usr/bin/env python3
"""
Test script to verify that asyncpg imports work correctly in the migrated codebase.
"""

import sys
import traceback

def test_imports():
    """Test that all the key modules can be imported without psycopg2 dependencies."""
    
    test_modules = [
        'odoo.async_sql_db',
        'odoo.modules.db',
        'odoo.cli.server',
        'odoo.tools.profiler',
        'odoo.tools.translate',
        'odoo.tools.safe_eval',
        'odoo.tools.populate',
        'odoo.http.request',
        'odoo.http.utils',
        'odoo.service.model',
        'odoo.service.async_db',
    ]
    
    print("🔍 Testing asyncpg-based imports...")
    
    failed_imports = []
    
    for module in test_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            if 'psycopg2' in str(e):
                print(f"❌ {module} - Still depends on psycopg2: {e}")
                failed_imports.append((module, str(e)))
            else:
                print(f"⚠️  {module} - Import error (may be expected): {e}")
        except Exception as e:
            print(f"⚠️  {module} - Other error: {e}")
    
    if failed_imports:
        print(f"\n❌ {len(failed_imports)} modules still have psycopg2 dependencies:")
        for module, error in failed_imports:
            print(f"  {module}: {error}")
        return False
    else:
        print(f"\n✅ All {len(test_modules)} modules successfully migrated from psycopg2!")
        return True

def test_asyncpg_availability():
    """Test that asyncpg is available and can be imported."""
    try:
        import asyncpg
        print("✅ asyncpg is available")
        print(f"   Version: {asyncpg.__version__}")
        return True
    except ImportError as e:
        print(f"❌ asyncpg is not available: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 Testing psycopg2 to asyncpg migration...")
    
    # Test asyncpg availability
    if not test_asyncpg_availability():
        return False
    
    # Test imports
    if not test_imports():
        return False
    
    print("\n🎉 Migration test successful!")
    print("✅ All modules have been successfully migrated from psycopg2 to asyncpg")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
