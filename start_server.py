#!/usr/bin/env python3
"""
Simple script to start Odoo server for testing
"""

import subprocess
import sys
import os
from pathlib import Path

def start_server():
    """Start Odoo server"""
    base_dir = Path(__file__).parent
    venv_python = base_dir / "venv" / "bin" / "python"
    odoo_bin = base_dir / "odoo-bin"
    config_file = base_dir / "odoo.conf"
    
    print("🚀 Starting Odoo server...")
    
    cmd = [
        str(venv_python),
        str(odoo_bin),
        "-c", str(config_file),
        "--dev=reload,qweb,werkzeug,xml",
        "--log-level=info"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        # Start the process and let it run
        process = subprocess.Popen(
            cmd,
            cwd=base_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("✅ Odoo server started")
        print("🌐 Access Odoo at: http://localhost:8069")
        print("📋 Process ID:", process.pid)
        
        # Print initial output
        for i, line in enumerate(process.stdout):
            print(f"[ODOO] {line.rstrip()}")
            if i > 50:  # Limit output
                break
                
        return process
        
    except Exception as e:
        print(f"❌ Failed to start Odoo: {e}")
        return None

if __name__ == "__main__":
    start_server()
