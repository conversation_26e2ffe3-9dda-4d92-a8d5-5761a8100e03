#!/usr/bin/env python3
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Comprehensive tests for WSGI to ASGI migration.
This test suite verifies that the async migration works correctly.
"""

import asyncio
import pytest
import httpx
import time
from unittest.mock import AsyncMock, patch

# Test imports
from odoo.async_sql_db import Async<PERSON>on<PERSON><PERSON><PERSON><PERSON>, AsyncCursor, async_db_connect
from odoo.async_api import AsyncEnvironment, AsyncRegistry
from odoo.http.asgi_application import AsyncApplication
from odoo.http.async_request_response import AsyncHTTPRequest, AsyncRequest
from odoo.service.async_db import AsyncDatabaseManager


class TestAsyncDatabaseLayer:
    """Test the async database layer."""
    
    @pytest.mark.asyncio
    async def test_async_connection_pool(self):
        """Test async connection pool functionality."""
        pool = AsyncConnectionPool(maxconn=5)
        
        # Mock connection info
        connection_info = {
            'database': 'test_db',
            'host': 'localhost',
            'port': 5432,
            'user': 'odoo',
            'password': 'odoo'
        }
        
        # Test connection borrowing (mocked)
        with patch('asyncpg.create_pool') as mock_create_pool:
            mock_pool = AsyncMock()
            mock_create_pool.return_value = mock_pool
            mock_connection = AsyncMock()
            mock_pool.acquire.return_value = mock_connection
            
            connection = await pool.borrow_connection('test_db', connection_info)
            assert connection is not None
            mock_create_pool.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_cursor_operations(self):
        """Test async cursor operations."""
        # Mock connection
        mock_connection = AsyncMock()
        mock_connection.execute.return_value = "OK"
        
        cursor = AsyncCursor(mock_connection, 'test_db')
        
        # Test execute
        result = await cursor.execute("SELECT 1")
        assert result == "OK"
        mock_connection.execute.assert_called_once()
        
        # Test transaction methods
        await cursor.begin()
        await cursor.commit()
        await cursor.rollback()

    @pytest.mark.asyncio
    async def test_async_db_connect(self):
        """Test async database connection."""
        with patch('odoo.async_sql_db.AsyncConnectionPool') as mock_pool_class:
            mock_pool = AsyncMock()
            mock_pool_class.return_value = mock_pool
            
            connection = await async_db_connect('test_db')
            assert connection is not None


class TestAsyncAPILayer:
    """Test the async API layer."""
    
    @pytest.mark.asyncio
    async def test_async_environment(self):
        """Test async environment creation and operations."""
        # Mock cursor
        mock_cursor = AsyncMock()
        mock_cursor.dbname = 'test_db'
        
        env = AsyncEnvironment(mock_cursor, 1, {})
        await env.init_async()
        
        # Test context operations
        new_env = env.with_context(test_key='test_value')
        assert 'test_key' in new_env.context
        assert new_env.context['test_key'] == 'test_value'
        
        # Test user operations
        sudo_env = env.sudo()
        assert sudo_env.su is True

    @pytest.mark.asyncio
    async def test_async_registry(self):
        """Test async registry operations."""
        registry = AsyncRegistry('test_db')
        await registry.init_async()
        
        # Test registry methods
        assert 'test_model' not in registry
        assert list(registry.keys()) == []


class TestAsyncHTTPLayer:
    """Test the async HTTP layer."""
    
    @pytest.mark.asyncio
    async def test_async_application(self):
        """Test ASGI application."""
        app = AsyncApplication()
        
        # Test static file resolution
        static_file = app.get_static_file('/web/static/src/js/test.js')
        # This will be None since we don't have actual static files in test
        
        # Test ASGI interface
        scope = {
            'type': 'http',
            'method': 'GET',
            'path': '/test',
            'headers': [],
        }
        
        received_messages = []
        sent_messages = []
        
        async def receive():
            return {'type': 'http.request', 'body': b''}
        
        async def send(message):
            sent_messages.append(message)
        
        # This should not raise an exception
        await app(scope, receive, send)
        assert len(sent_messages) > 0

    @pytest.mark.asyncio
    async def test_async_request_response(self):
        """Test async request/response handling."""
        # Mock Starlette request
        mock_starlette_request = AsyncMock()
        mock_starlette_request.method = 'GET'
        mock_starlette_request.url.path = '/test'
        mock_starlette_request.url.query = ''
        mock_starlette_request.headers = {}
        mock_starlette_request.client.host = '127.0.0.1'
        mock_starlette_request.body.return_value = b''
        
        async_request = AsyncHTTPRequest(mock_starlette_request)
        
        assert async_request.method == 'GET'
        assert async_request.path == '/test'
        assert async_request.remote_addr == '127.0.0.1'
        
        body = await async_request.get_body()
        assert body == b''


class TestAsyncServiceLayer:
    """Test the async service layer."""
    
    @pytest.mark.asyncio
    async def test_async_database_manager(self):
        """Test async database management operations."""
        manager = AsyncDatabaseManager()
        
        # Mock database operations
        with patch('odoo.service.async_db.async_db_connect') as mock_connect:
            mock_connection = AsyncMock()
            mock_cursor = AsyncMock()
            mock_connection.cursor.return_value.__aenter__.return_value = mock_cursor
            mock_connect.return_value = mock_connection
            
            # Test database existence check
            exists = await manager.database_exists('test_db')
            # This will depend on the mock implementation
            
            # Test database listing
            databases = await manager.list_databases()
            assert isinstance(databases, list)


class TestAsyncPerformance:
    """Test async performance improvements."""
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """Test handling multiple concurrent requests."""
        app = AsyncApplication()
        
        async def make_request():
            scope = {
                'type': 'http',
                'method': 'GET',
                'path': '/test',
                'headers': [],
            }
            
            sent_messages = []
            
            async def receive():
                return {'type': 'http.request', 'body': b''}
            
            async def send(message):
                sent_messages.append(message)
            
            await app(scope, receive, send)
            return len(sent_messages)
        
        # Make 10 concurrent requests
        start_time = time.time()
        tasks = [make_request() for _ in range(10)]
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # All requests should complete
        assert all(result > 0 for result in results)
        
        # Should complete relatively quickly (less than 5 seconds for 10 requests)
        assert end_time - start_time < 5.0

    @pytest.mark.asyncio
    async def test_database_connection_pooling(self):
        """Test async database connection pooling performance."""
        pool = AsyncConnectionPool(maxconn=10)
        
        async def get_connection():
            connection_info = {
                'database': 'test_db',
                'host': 'localhost',
                'port': 5432,
            }
            
            with patch('asyncpg.create_pool') as mock_create_pool:
                mock_pool = AsyncMock()
                mock_create_pool.return_value = mock_pool
                mock_connection = AsyncMock()
                mock_pool.acquire.return_value = mock_connection
                
                connection = await pool.borrow_connection('test_db', connection_info)
                await pool.return_connection(connection)
                return True
        
        # Test concurrent connection usage
        tasks = [get_connection() for _ in range(20)]
        results = await asyncio.gather(*tasks)
        
        # All connections should be acquired successfully
        assert all(results)


class TestMigrationCompatibility:
    """Test backward compatibility after migration."""
    
    def test_import_compatibility(self):
        """Test that existing imports still work."""
        # These should not raise ImportError
        try:
            from odoo import http
            from odoo.http import Controller, route
            from odoo import api
            from odoo.api import Environment
        except ImportError as e:
            pytest.fail(f"Import compatibility broken: {e}")

    @pytest.mark.asyncio
    async def test_mixed_sync_async_operations(self):
        """Test that sync and async operations can coexist."""
        # This tests the transition period where both sync and async code exist
        
        # Mock sync operation
        def sync_operation():
            return "sync_result"
        
        # Mock async operation
        async def async_operation():
            await asyncio.sleep(0.01)  # Simulate async work
            return "async_result"
        
        # Test running both
        sync_result = sync_operation()
        async_result = await async_operation()
        
        assert sync_result == "sync_result"
        assert async_result == "async_result"


if __name__ == '__main__':
    # Run tests
    pytest.main([__file__, '-v'])
