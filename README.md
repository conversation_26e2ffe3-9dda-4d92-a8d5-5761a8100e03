# Odoo

[![Build Status](https://runbot.odoo.com/runbot/badge/flat/1/master.svg)](https://runbot.odoo.com/runbot)
[![Tech Doc](https://img.shields.io/badge/master-docs-875A7B.svg?style=flat&colorA=8F8F8F)](https://www.odoo.com/documentation/master)
[![Help](https://img.shields.io/badge/master-help-875A7B.svg?style=flat&colorA=8F8F8F)](https://www.odoo.com/forum/help-1)
[![Nightly Builds](https://img.shields.io/badge/master-nightly-875A7B.svg?style=flat&colorA=8F8F8F)](https://nightly.odoo.com/)

Odoo is a suite of web based open source business apps.

The main Odoo Apps include an [Open Source CRM](https://www.odoo.com/page/crm),
[Website Builder](https://www.odoo.com/app/website),
[eCommerce](https://www.odoo.com/app/ecommerce),
[Warehouse Management](https://www.odoo.com/app/inventory),
[Project Management](https://www.odoo.com/app/project),
[Billing &amp; Accounting](https://www.odoo.com/app/accounting),
[Point of Sale](https://www.odoo.com/app/point-of-sale-shop),
[Human Resources](https://www.odoo.com/app/employees),
[Marketing](https://www.odoo.com/app/social-marketing),
[Manufacturing](https://www.odoo.com/app/manufacturing),
[...](https://www.odoo.com/)

Odoo Apps can be used as stand-alone applications, but they also integrate seamlessly so you get
a full-featured [Open Source ERP](https://www.odoo.com) when you install several Apps.

## Getting started with Odoo

For a standard installation please follow the [Setup instructions](https://www.odoo.com/documentation/master/administration/install/install.html)
from the documentation.

### Development Setup with Odoo Runner

For development purposes, this repository includes an **Odoo Runner** (`odoo_runner.py`) that provides an interactive way to manage your Odoo development server. The runner is the **recommended way** to start and manage Odoo during development.

#### Features:
- 🚀 **Interactive server management** - Start, stop, restart Odoo with simple commands
- 🐳 **Automatic PostgreSQL management** - Handles Docker PostgreSQL container lifecycle
- 📊 **Real-time status monitoring** - Shows current state of services
- 🔄 **Hot reload support** - Includes development flags for faster iteration
- 📝 **Live log monitoring** - View Odoo logs in real-time

#### Quick Start:
```bash
# Start the interactive runner
python3 odoo_runner.py

# Available commands:
# s - Start Odoo server (without database initialization)
# r - Restart Odoo server
# k - Kill Odoo process
# d - Toggle Docker PostgreSQL (start/stop)
# l - Show current status
# h - Show help
# e - Exit runner
```

#### Why use the Odoo Runner?
- **Better development workflow**: No need to manually manage database initialization
- **Simplified setup**: Automatically handles PostgreSQL container management
- **Consistent environment**: Ensures proper development flags and configuration
- **Time-saving**: Interactive commands eliminate repetitive terminal operations

To learn the software, we recommend the [Odoo eLearning](https://www.odoo.com/slides),
or [Scale-up, the business game](https://www.odoo.com/page/scale-up-business-game).
Developers can start with [the developer tutorials](https://www.odoo.com/documentation/master/developer/howtos.html).

## Security

If you believe you have found a security issue, check our [Responsible Disclosure page](https://www.odoo.com/security-report)
for details and get in touch with us via email.
