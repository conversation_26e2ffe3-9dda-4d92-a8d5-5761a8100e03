# ASGI Handler sample configuration file.
#
# Change the appropriate settings below, in order to provide the parameters
# that would normally be passed in the command-line.
# (at least conf['addons_path'])
#
# For ASGI servers like Uvicorn, this should work:
#   $ uvicorn --host 0.0.0.0 --port 8069 --app odoo-asgi:application
#
# For Hypercorn:
#   $ hypercorn --bind 0.0.0.0:8069 odoo-asgi:application
#
# For Gunicorn with Uvicorn workers:
#   $ gunicorn -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8069 odoo-asgi:application

import asyncio
import odoo

#----------------------------------------------------------
# Common Configuration
#----------------------------------------------------------

# Equivalent of --load command-line option
odoo.conf.server_wide_modules = ['base', 'web']
conf = odoo.tools.config

# Path to the Odoo Addons repository (comma-separated for multiple locations)
#conf['addons_path'] = './odoo/addons,./addons'

# Optional database config if not using local socket
#conf['db_name'] = 'mycompany'
#conf['db_host'] = 'localhost'
#conf['db_user'] = 'foo'
#conf['db_port'] = 5432
#conf['db_password'] = 'secret'

# Enable ASGI mode
conf['asgi_enable'] = True

#----------------------------------------------------------
# ASGI Application
#----------------------------------------------------------

# Import the async application
from odoo.http.asgi_application import async_root

# Load server-wide modules
odoo.service.server.load_server_wide_modules()

# ASGI application instance
application = async_root

#----------------------------------------------------------
# Uvicorn Configuration (if using uvicorn directly)
#----------------------------------------------------------

# Uvicorn configuration for production
UVICORN_CONFIG = {
    'host': '0.0.0.0',
    'port': 8069,
    'workers': 4,  # Number of worker processes
    'loop': 'asyncio',
    'http': 'httptools',
    'lifespan': 'on',
    'access_log': True,
    'log_level': 'info',
    
    # SSL Configuration (uncomment and configure for HTTPS)
    # 'ssl_keyfile': '/path/to/keyfile.key',
    # 'ssl_certfile': '/path/to/certfile.crt',
    
    # Performance tuning
    'backlog': 2048,
    'limit_concurrency': 1000,
    'limit_max_requests': 10000,
    'timeout_keep_alive': 5,
}

#----------------------------------------------------------
# Hypercorn Configuration (if using hypercorn)
#----------------------------------------------------------

# Hypercorn configuration for production
HYPERCORN_CONFIG = {
    'bind': ['0.0.0.0:8069'],
    'workers': 4,
    'worker_class': 'asyncio',
    'access_log': '-',
    'error_log': '-',
    'log_level': 'info',
    
    # SSL Configuration (uncomment and configure for HTTPS)
    # 'keyfile': '/path/to/keyfile.key',
    # 'certfile': '/path/to/certfile.crt',
    
    # Performance tuning
    'backlog': 2048,
    'max_requests': 10000,
    'keep_alive_timeout': 5,
}

#----------------------------------------------------------
# Development Server (for testing)
#----------------------------------------------------------

async def run_development_server():
    """Run development ASGI server."""
    import uvicorn
    
    config = uvicorn.Config(
        app=application,
        host='127.0.0.1',
        port=8069,
        log_level='debug',
        reload=True,  # Auto-reload on code changes
        reload_dirs=['./odoo', './addons'],
    )
    
    server = uvicorn.Server(config)
    await server.serve()


if __name__ == '__main__':
    # Run development server
    asyncio.run(run_development_server())

#----------------------------------------------------------
# Production Deployment Examples
#----------------------------------------------------------

"""
# Example 1: Using Uvicorn directly
uvicorn --host 0.0.0.0 --port 8069 --workers 4 odoo-asgi:application

# Example 2: Using Gunicorn with Uvicorn workers
gunicorn -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8069 odoo-asgi:application

# Example 3: Using Hypercorn
hypercorn --bind 0.0.0.0:8069 --workers 4 odoo-asgi:application

# Example 4: Using Docker with Uvicorn
docker run -p 8069:8069 -v $(pwd):/app -w /app python:3.12 \
    sh -c "pip install uvicorn && uvicorn --host 0.0.0.0 --port 8069 odoo-asgi:application"

# Example 5: Systemd service file (/etc/systemd/system/odoo-asgi.service)
[Unit]
Description=Odoo ASGI Server
After=network.target

[Service]
Type=exec
User=odoo
Group=odoo
WorkingDirectory=/opt/odoo
Environment=PATH=/opt/odoo/venv/bin
ExecStart=/opt/odoo/venv/bin/uvicorn --host 0.0.0.0 --port 8069 --workers 4 odoo-asgi:application
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target

# Example 6: Nginx configuration for reverse proxy
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8069;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
"""
