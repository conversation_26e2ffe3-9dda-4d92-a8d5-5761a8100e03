#!/usr/bin/env python3
"""
Test script to verify that the HTTP module refactoring maintains backward compatibility.
"""

def test_imports():
    """Test that all expected classes and functions can be imported."""
    print("Testing imports...")
    
    try:
        # Test main imports
        from odoo.http import (
            Controller, route, Request, Response, Session, 
            FilesystemSessionStore, Stream, GeoIP, Application,
            HttpDispatcher, JsonRPCDispatcher, request, root
        )
        print("✅ All main classes imported successfully")
        
        # Test utility functions
        from odoo.http import (
            get_default_session, content_disposition, db_list, db_filter,
            dispatch_rpc, serialize_exception
        )
        print("✅ All utility functions imported successfully")
        
        # Test constants
        from odoo.http import (
            DEFAULT_MAX_CONTENT_LENGTH, SESSION_LIFETIME, 
            JSON_MIMETYPES, ROUTING_KEYS
        )
        print("✅ All constants imported successfully")
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    
    return True


def test_controller_creation():
    """Test that Controller class can be instantiated and route decorator works."""
    print("\nTesting Controller functionality...")
    
    try:
        from odoo.http import Controller, route
        
        class TestController(Controller):
            @route('/test', type='http', auth='public')
            def test_method(self):
                return "Hello World"
        
        controller = TestController()
        print("✅ Controller class works correctly")
        
        # Check that the route decorator added the routing info
        if hasattr(controller.test_method, 'original_routing'):
            print("✅ Route decorator works correctly")
        else:
            print("❌ Route decorator not working")
            return False
            
    except Exception as e:
        print(f"❌ Controller test failed: {e}")
        return False
    
    return True


def test_session_creation():
    """Test that Session class can be instantiated."""
    print("\nTesting Session functionality...")
    
    try:
        from odoo.http import Session, get_default_session
        
        session_data = get_default_session()
        session = Session(session_data, 'test_sid', new=True)
        
        # Test basic session operations
        session['test_key'] = 'test_value'
        assert session['test_key'] == 'test_value'
        assert session.test_key == 'test_value'
        
        print("✅ Session class works correctly")
        
    except Exception as e:
        print(f"❌ Session test failed: {e}")
        return False
    
    return True


def test_stream_creation():
    """Test that Stream class can be instantiated."""
    print("\nTesting Stream functionality...")
    
    try:
        from odoo.http import Stream
        
        # Test data stream
        test_data = b"Hello World"
        stream = Stream(
            type='data',
            data=test_data,
            mimetype='text/plain',
            size=len(test_data)
        )
        
        assert stream.read() == test_data
        print("✅ Stream class works correctly")
        
    except Exception as e:
        print(f"❌ Stream test failed: {e}")
        return False
    
    return True


def main():
    """Run all tests."""
    print("🧪 Testing HTTP module refactoring...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_controller_creation,
        test_session_creation,
        test_stream_creation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! HTTP module refactoring is successful.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    main()
