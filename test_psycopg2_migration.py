#!/usr/bin/env python3
"""
Test script to verify that all psycopg2 references have been successfully migrated to asyncpg.
"""

import ast
import os
import sys
from pathlib import Path

def find_psycopg2_imports(directory):
    """Find any remaining psycopg2 imports in Python files."""
    psycopg2_files = []
    
    for root, dirs, files in os.walk(directory):
        # Skip virtual environment and cache directories
        dirs[:] = [d for d in dirs if d not in ('venv', '__pycache__', '.git')]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    # Parse the AST to find imports
                    try:
                        tree = ast.parse(content)
                        for node in ast.walk(tree):
                            if isinstance(node, ast.Import):
                                for alias in node.names:
                                    if alias.name.startswith('psycopg2'):
                                        psycopg2_files.append((file_path, f"import {alias.name}", node.lineno))
                            elif isinstance(node, ast.ImportFrom):
                                if node.module and node.module.startswith('psycopg2'):
                                    names = [alias.name for alias in node.names]
                                    psycopg2_files.append((file_path, f"from {node.module} import {', '.join(names)}", node.lineno))
                    except SyntaxError:
                        # Skip files with syntax errors
                        continue
                        
                except (UnicodeDecodeError, PermissionError):
                    # Skip files that can't be read
                    continue
    
    return psycopg2_files

def find_psycopg2_usage(directory):
    """Find any remaining psycopg2 usage patterns in Python files."""
    usage_patterns = []
    
    for root, dirs, files in os.walk(directory):
        # Skip virtual environment and cache directories
        dirs[:] = [d for d in dirs if d not in ('venv', '__pycache__', '.git')]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        
                    for i, line in enumerate(lines, 1):
                        # Skip comments
                        if line.strip().startswith('#'):
                            continue
                            
                        # Look for psycopg2 usage patterns
                        if 'psycopg2.' in line and not line.strip().startswith('#'):
                            usage_patterns.append((file_path, line.strip(), i))
                            
                except (UnicodeDecodeError, PermissionError):
                    # Skip files that can't be read
                    continue
    
    return usage_patterns

def main():
    """Main test function."""
    print("🔍 Checking for remaining psycopg2 references...")
    
    # Find remaining imports
    imports = find_psycopg2_imports('.')
    if imports:
        print("❌ Found remaining psycopg2 imports:")
        for file_path, import_line, line_no in imports:
            print(f"  {file_path}:{line_no} - {import_line}")
        return False
    else:
        print("✅ No psycopg2 imports found")
    
    # Find remaining usage patterns
    usage = find_psycopg2_usage('.')
    if usage:
        print("❌ Found remaining psycopg2 usage patterns:")
        for file_path, line, line_no in usage:
            print(f"  {file_path}:{line_no} - {line}")
        return False
    else:
        print("✅ No psycopg2 usage patterns found")
    
    print("\n🎉 Migration verification successful!")
    print("✅ All psycopg2 references have been successfully replaced with asyncpg equivalents")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
